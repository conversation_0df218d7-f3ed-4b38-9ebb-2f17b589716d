// Component Loader - Initializes global components
class ComponentLoader {
  constructor() {
    this.navigationComponent = null;
    this.footerComponent = null;
    this.analyticsManager = null;
    this.seoManager = null;
  }

  // Initialize all components
  init() {
    this.initNavigation();
    this.initFooter();
    this.initAnalytics();
    this.initSEO();
    this.setupAuthStateListener();
  }

  initNavigation() {
    const navContainer = document.getElementById('navigation-container');
    if (navContainer) {
      this.navigationComponent = new NavigationComponent();
      this.navigationComponent.render();
    }
  }

  initFooter() {
    const footerContainer = document.getElementById('footer-container');
    if (footerContainer) {
      this.footerComponent = new FooterComponent();
      this.footerComponent.render();
    }
  }

  initAnalytics() {
    if (typeof AnalyticsManager !== 'undefined') {
      this.analyticsManager = new AnalyticsManager();
      this.analyticsManager.init();

      // Make analytics globally available
      window.analytics = this.analyticsManager;
    }
  }

  initSEO() {
    if (typeof SEOManager !== 'undefined') {
      this.seoManager = new SEOManager();

      // Determine page type from URL
      const path = window.location.pathname;
      let pageName = 'index';

      if (path.includes('dashboard')) pageName = 'dashboard';
      else if (path.includes('login')) pageName = 'login';
      else if (path.includes('register')) pageName = 'register';
      else if (path.includes('share')) pageName = 'share';

      // Get page-specific SEO data
      const pageSEO = SEOManager.getPageSEO(pageName);

      // Initialize SEO for this page
      this.seoManager.init(pageSEO);

      // Make SEO manager globally available
      window.seo = this.seoManager;
    }
  }

  // Listen for auth state changes and update navigation
  setupAuthStateListener() {
    // Listen for storage changes (when user logs in/out in another tab)
    window.addEventListener('storage', (e) => {
      if (e.key === 'user') {
        this.updateNavigation();
      }
    });

    // Listen for custom auth events
    window.addEventListener('authStateChanged', () => {
      this.updateNavigation();
    });
  }

  updateNavigation() {
    if (this.navigationComponent) {
      this.navigationComponent.updateAuthState();
    }
  }

  // Static method to trigger auth state change event
  static triggerAuthStateChange() {
    window.dispatchEvent(new CustomEvent('authStateChanged'));
  }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Only initialize if core components are available
  if (typeof NavigationComponent !== 'undefined' && typeof FooterComponent !== 'undefined') {
    window.componentLoader = new ComponentLoader();
    window.componentLoader.init();
  }
});

// Export for use in other scripts
window.ComponentLoader = ComponentLoader;
