// SEO Manager - Centralized SEO optimization
class SEOManager {
  constructor() {
    this.siteName = 'mermantic';
    this.siteUrl = 'https://mermantic.net';
    this.defaultImage = '/img/og-image.png';
    this.twitterHandle = '@mermantic'; // Update when you have Twitter
    
    // Default SEO data
    this.defaultSEO = {
      title: 'mermantic - Create and Share Mermaid Diagrams',
      description: 'Create, edit, and share beautiful Mermaid diagrams online. Free diagram tool with live preview, multiple themes, and easy sharing.',
      keywords: 'mermaid, diagrams, flowchart, sequence diagram, class diagram, online tool, visualization',
      image: this.defaultImage,
      type: 'website'
    };
  }

  // Initialize SEO for the current page
  init(pageData = {}) {
    const seoData = { ...this.defaultSEO, ...pageData };
    
    this.setTitle(seoData.title);
    this.setMetaDescription(seoData.description);
    this.setMetaKeywords(seoData.keywords);
    this.setCanonicalUrl(seoData.url);
    this.setOpenGraphTags(seoData);
    this.setTwitterCardTags(seoData);
    this.setStructuredData(seoData);
    this.optimizeImages();
    
    console.log('🔍 SEO initialized for:', seoData.title);
  }

  // Set page title
  setTitle(title) {
    document.title = title;
    this.updateMetaTag('property', 'og:title', title);
    this.updateMetaTag('name', 'twitter:title', title);
  }

  // Set meta description
  setMetaDescription(description) {
    this.updateMetaTag('name', 'description', description);
    this.updateMetaTag('property', 'og:description', description);
    this.updateMetaTag('name', 'twitter:description', description);
  }

  // Set meta keywords
  setMetaKeywords(keywords) {
    this.updateMetaTag('name', 'keywords', keywords);
  }

  // Set canonical URL
  setCanonicalUrl(url) {
    const canonicalUrl = url || window.location.href;
    
    // Remove or update existing canonical link
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.rel = 'canonical';
      document.head.appendChild(canonical);
    }
    canonical.href = canonicalUrl;
    
    // Also set in Open Graph
    this.updateMetaTag('property', 'og:url', canonicalUrl);
  }

  // Set Open Graph tags
  setOpenGraphTags(data) {
    const ogTags = {
      'og:site_name': this.siteName,
      'og:type': data.type || 'website',
      'og:title': data.title,
      'og:description': data.description,
      'og:image': this.getFullUrl(data.image),
      'og:url': data.url || window.location.href,
      'og:locale': 'en_US'
    };

    Object.entries(ogTags).forEach(([property, content]) => {
      this.updateMetaTag('property', property, content);
    });

    // Additional image properties
    this.updateMetaTag('property', 'og:image:width', '1200');
    this.updateMetaTag('property', 'og:image:height', '630');
    this.updateMetaTag('property', 'og:image:type', 'image/png');
  }

  // Set Twitter Card tags
  setTwitterCardTags(data) {
    const twitterTags = {
      'twitter:card': 'summary_large_image',
      'twitter:site': this.twitterHandle,
      'twitter:creator': this.twitterHandle,
      'twitter:title': data.title,
      'twitter:description': data.description,
      'twitter:image': this.getFullUrl(data.image)
    };

    Object.entries(twitterTags).forEach(([name, content]) => {
      this.updateMetaTag('name', name, content);
    });
  }

  // Set structured data (JSON-LD)
  setStructuredData(data) {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": this.siteName,
      "description": data.description,
      "url": this.siteUrl,
      "applicationCategory": "DesignApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "creator": {
        "@type": "Organization",
        "name": "mermantic",
        "url": this.siteUrl
      },
      "featureList": [
        "Create Mermaid diagrams",
        "Live preview",
        "Multiple diagram types",
        "Save and share diagrams",
        "Multiple themes",
        "Export to PNG/SVG"
      ]
    };

    // Add or update structured data script
    let script = document.querySelector('script[type="application/ld+json"]');
    if (!script) {
      script = document.createElement('script');
      script.type = 'application/ld+json';
      document.head.appendChild(script);
    }
    script.textContent = JSON.stringify(structuredData, null, 2);
  }

  // Update or create meta tag
  updateMetaTag(attribute, value, content) {
    let meta = document.querySelector(`meta[${attribute}="${value}"]`);
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute(attribute, value);
      document.head.appendChild(meta);
    }
    meta.content = content;
  }

  // Get full URL for images
  getFullUrl(path) {
    if (!path) return this.getFullUrl(this.defaultImage);
    if (path.startsWith('http')) return path;
    return this.siteUrl + path;
  }

  // Optimize images with alt text
  optimizeImages() {
    const images = document.querySelectorAll('img:not([alt])');
    images.forEach(img => {
      // Set default alt text if missing
      if (!img.alt) {
        const src = img.src;
        if (src.includes('google-icon')) {
          img.alt = 'Google';
        } else if (src.includes('logo')) {
          img.alt = 'mermantic logo';
        } else {
          img.alt = 'Diagram image';
        }
      }
    });
  }

  // Page-specific SEO configurations
  static getPageSEO(pageName) {
    const pageConfigs = {
      'index': {
        title: 'mermantic - Create and Share Mermaid Diagrams Online',
        description: 'Create, edit, and share beautiful Mermaid diagrams online. Free diagram tool with live preview, multiple themes, flowcharts, sequence diagrams, and easy sharing.',
        keywords: 'mermaid diagrams, flowchart maker, sequence diagram, class diagram, online diagram tool, free diagram software, visualization tool',
        type: 'website'
      },
      'dashboard': {
        title: 'Dashboard - mermantic',
        description: 'Manage your Mermaid diagrams. Create, edit, organize, and share your diagrams from your personal dashboard.',
        keywords: 'diagram dashboard, manage diagrams, mermaid editor, diagram organizer',
        type: 'website'
      },
      'login': {
        title: 'Login - mermantic',
        description: 'Login to your mermantic account to access your saved diagrams and create new ones.',
        keywords: 'login, sign in, mermaid diagrams, account access',
        type: 'website'
      },
      'register': {
        title: 'Register - mermantic',
        description: 'Create a free mermantic account to save, organize, and share your Mermaid diagrams.',
        keywords: 'register, sign up, create account, free diagram tool',
        type: 'website'
      },
      'share': {
        title: 'Shared Diagram - mermantic',
        description: 'View a shared Mermaid diagram created with mermantic.',
        keywords: 'shared diagram, mermaid diagram, view diagram',
        type: 'article'
      }
    };

    return pageConfigs[pageName] || {};
  }

  // Dynamic SEO for shared diagrams
  static getSharedDiagramSEO(diagramData) {
    if (!diagramData) return SEOManager.getPageSEO('share');
    
    return {
      title: `${diagramData.title} - Shared Diagram | mermantic`,
      description: `View "${diagramData.title}" - a Mermaid diagram shared on mermantic. ${diagramData.notes || 'Interactive diagram with zoom and export features.'}`,
      keywords: `${diagramData.title}, mermaid diagram, shared diagram, flowchart, visualization`,
      type: 'article',
      image: diagramData.preview_image || '/img/og-image.png'
    };
  }
}

// Export for use in other scripts
window.SEOManager = SEOManager;
