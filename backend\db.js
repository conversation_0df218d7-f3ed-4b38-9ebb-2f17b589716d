const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

const dbPath = process.env.DB_PATH || path.join(__dirname, '../database.sqlite');
const jsonDbPath = path.join(__dirname, '../database.json');

// Initialize SQLite database
const db = new Database(dbPath);

console.log('Connected to SQLite database at:', dbPath);

// Create tables if they don't exist
db.exec(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT,
    google_id TEXT UNIQUE,
    profile_picture TEXT,
    auth_type TEXT DEFAULT 'local',
    reset_token TEXT,
    reset_token_expires DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  CREATE TABLE IF NOT EXISTS charts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    public BOOLEAN DEFAULT 0,
    share_id TEXT UNIQUE,
    folder TEXT DEFAULT '',
    notes TEXT DEFAULT '',
    theme TEXT DEFAULT 'dark',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
  );

  CREATE TABLE IF NOT EXISTS sessions (
    sid TEXT PRIMARY KEY,
    sess TEXT NOT NULL,
    expire DATETIME NOT NULL
  );
`);

// Add theme column to existing charts table if it doesn't exist
try {
  db.exec(`ALTER TABLE charts ADD COLUMN theme TEXT DEFAULT 'dark'`);
  console.log('Added theme column to charts table');
} catch (error) {
  // Column already exists, ignore error
  if (!error.message.includes('duplicate column name')) {
    console.error('Error adding theme column:', error.message);
  }
}

// Migrate data from JSON database if it exists and SQLite is empty
function migrateFromJson() {
  if (fs.existsSync(jsonDbPath)) {
    try {
      const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
      if (userCount.count === 0) {
        console.log('Migrating data from JSON database to SQLite...');
        const jsonData = JSON.parse(fs.readFileSync(jsonDbPath, 'utf8'));

        // Migrate users
        if (jsonData.users && jsonData.users.length > 0) {
          const insertUser = db.prepare(`
            INSERT INTO users (id, username, email, password, google_id, profile_picture, auth_type, reset_token, reset_token_expires, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);

          for (const user of jsonData.users) {
            insertUser.run(
              user.id,
              user.username,
              user.email,
              user.password,
              user.google_id || null,
              user.profile_picture || null,
              user.auth_type || 'local',
              user.reset_token || null,
              user.reset_token_expires || null,
              user.created_at || new Date().toISOString()
            );
          }
          console.log(`Migrated ${jsonData.users.length} users from JSON to SQLite`);
        }

        // Migrate charts
        if (jsonData.charts && jsonData.charts.length > 0) {
          const insertChart = db.prepare(`
            INSERT INTO charts (id, user_id, title, content, public, share_id, folder, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);

          for (const chart of jsonData.charts) {
            insertChart.run(
              chart.id,
              chart.user_id,
              chart.title,
              chart.content,
              chart.public || 0,
              chart.share_id || null,
              chart.folder || '',
              chart.notes || '',
              chart.created_at || new Date().toISOString(),
              chart.updated_at || new Date().toISOString()
            );
          }
          console.log(`Migrated ${jsonData.charts.length} charts from JSON to SQLite`);
        }

        console.log('Migration from JSON to SQLite completed successfully');
      }
    } catch (error) {
      console.error('Error migrating from JSON database:', error);
    }
  }
}

// Run migration on startup
migrateFromJson();

// Create wrapper object to maintain compatibility with existing code
const dbWrapper = {
  // Run method for INSERT/UPDATE operations
  run: function(sql, params, callback) {
    try {
      console.log('DB operation:', sql, params);
      const stmt = db.prepare(sql);
      const result = stmt.run(...params);

      // Create context object with SQLite-like properties
      const context = {
        lastID: result.lastInsertRowid,
        changes: result.changes
      };

      if (callback) callback.call(context, null);
    } catch (error) {
      console.error('Database run error:', error);
      if (callback) callback(error);
    }
  },

  // Get method for single record queries
  get: function(sql, params, callback) {
    try {
      const stmt = db.prepare(sql);
      const result = stmt.get(...params);
      if (callback) callback(null, result);
    } catch (error) {
      console.error('Database get error:', error);
      if (callback) callback(error, null);
    }
  },

  // All method for multiple record queries
  all: function(sql, params, callback) {
    // Handle case where params is actually the callback
    if (typeof params === 'function') {
      callback = params;
      params = [];
    }

    try {
      const stmt = db.prepare(sql);
      const results = stmt.all(...params);
      if (callback) callback(null, results);
    } catch (error) {
      console.error('Database all error:', error);
      if (callback) callback(error, []);
    }
  },

  // Serialize method for compatibility
  serialize: function(callback) {
    if (callback) callback();
  }
};

// Helper functions for user operations
dbWrapper.createUser = function(username, email, hashedPassword, callback) {
  try {
    const stmt = db.prepare(`
      INSERT INTO users (username, email, password, auth_type, created_at)
      VALUES (?, ?, ?, 'local', datetime('now'))
    `);
    const result = stmt.run(username, email, hashedPassword);
    if (callback) callback(null, result.lastInsertRowid);
  } catch (error) {
    console.error('Error creating user:', error);
    if (callback) callback(error);
  }
};

dbWrapper.updateUserResetToken = function(email, resetToken, expires, callback) {
  try {
    const stmt = db.prepare(`
      UPDATE users SET reset_token = ?, reset_token_expires = ?
      WHERE email = ?
    `);
    const result = stmt.run(resetToken, expires, email);
    if (result.changes > 0) {
      if (callback) callback(null);
    } else {
      if (callback) callback(new Error('User not found'));
    }
  } catch (error) {
    console.error('Error updating reset token:', error);
    if (callback) callback(error);
  }
};

dbWrapper.resetUserPassword = function(userId, hashedPassword, callback) {
  try {
    const stmt = db.prepare(`
      UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL
      WHERE id = ?
    `);
    const result = stmt.run(hashedPassword, userId);
    if (result.changes > 0) {
      if (callback) callback(null);
    } else {
      if (callback) callback(new Error('User not found'));
    }
  } catch (error) {
    console.error('Error resetting password:', error);
    if (callback) callback(error);
  }
};

// Session management methods
dbWrapper.getSession = function(sid, callback) {
  try {
    const stmt = db.prepare('SELECT sess FROM sessions WHERE sid = ? AND expire > datetime("now")');
    const result = stmt.get(sid);
    if (callback) callback(null, result ? JSON.parse(result.sess) : null);
  } catch (error) {
    console.error('Error getting session:', error);
    if (callback) callback(error, null);
  }
};

dbWrapper.setSession = function(sid, session, callback) {
  try {
    const expire = new Date(Date.now() + (session.cookie?.maxAge || 86400000)); // Default 24 hours
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO sessions (sid, sess, expire)
      VALUES (?, ?, ?)
    `);
    stmt.run(sid, JSON.stringify(session), expire.toISOString());
    if (callback) callback(null);
  } catch (error) {
    console.error('Error setting session:', error);
    if (callback) callback(error);
  }
};

dbWrapper.destroySession = function(sid, callback) {
  try {
    const stmt = db.prepare('DELETE FROM sessions WHERE sid = ?');
    stmt.run(sid);
    if (callback) callback(null);
  } catch (error) {
    console.error('Error destroying session:', error);
    if (callback) callback(error);
  }
};

dbWrapper.clearExpiredSessions = function(callback) {
  try {
    const stmt = db.prepare('DELETE FROM sessions WHERE expire <= datetime("now")');
    const result = stmt.run();
    console.log(`Cleared ${result.changes} expired sessions`);
    if (callback) callback(null);
  } catch (error) {
    console.error('Error clearing expired sessions:', error);
    if (callback) callback(error);
  }
};

// Expose the raw database instance for the session store
dbWrapper.db = db;

// Graceful shutdown
process.on('exit', () => {
  db.close();
});

process.on('SIGINT', () => {
  db.close();
  process.exit(0);
});

module.exports = dbWrapper;
