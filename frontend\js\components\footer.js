// Global Footer Component
class FooterComponent {
  constructor() {
    this.currentYear = new Date().getFullYear();
  }

  getFooterHTML() {
    return `
      <footer>
        <div class="container">
          <div class="footer-content">
            <div class="footer-section">
              <p>&copy; ${this.currentYear} mermantic. All rights reserved.</p>
              <div class="version-info">
                <span class="version-badge">v0.15 beta</span>
              </div>
            </div>
            <div class="footer-section">
              <nav class="footer-nav">
                <a href="/">Home</a>
                <a href="/dashboard.html">Dashboard</a>
                <a href="https://mermaid.js.org/intro/" target="_blank" rel="noopener" class="external-link" aria-label="Mermaid Docs (opens in new tab)">
                  Mermaid Docs
                  <svg class="external-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    <polyline points="15,3 21,3 21,9"></polyline>
                    <line x1="10" y1="14" x2="21" y2="3"></line>
                  </svg>
                </a>
              </nav>
            </div>
            <div class="footer-section">
              <div class="footer-info">
                <span>Create and share beautiful diagrams</span>
                <div class="footer-contact">
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>
            </div>
          </div>
          <div class="footer-ai-disclosure">
            <p><em>*Information italicized with a ✨ notation denotes text that was created wholly or partially by Artificial Intelligence.</em></p>
          </div>
        </div>
      </footer>
    `;
  }

  render(containerId = 'footer-container') {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('Footer container not found:', containerId);
      return;
    }

    container.innerHTML = this.getFooterHTML();
  }

  // Static method for simple footer
  static getSimpleFooter() {
    const currentYear = new Date().getFullYear();
    return `
      <footer>
        <div class="container">
          <p>&copy; ${currentYear} mermantic. All rights reserved.</p>
        </div>
      </footer>
    `;
  }
}

// Export for use in other scripts
window.FooterComponent = FooterComponent;
