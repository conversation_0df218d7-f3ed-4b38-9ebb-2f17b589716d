// Global Navigation Component
class NavigationComponent {
  constructor() {
    this.currentPage = this.getCurrentPage();
    this.user = this.getUser();
  }

  getCurrentPage() {
    const path = window.location.pathname;
    if (path === '/' || path === '/index.html') return 'home';
    if (path === '/login.html') return 'login';
    if (path === '/register.html') return 'register';
    if (path === '/dashboard.html') return 'dashboard';
    if (path === '/gallery.html') return 'gallery';
    if (path === '/forgot-password.html') return 'forgot-password';
    if (path === '/reset-password.html') return 'reset-password';
    if (path === '/share.html') return 'share';
    return 'other';
  }

  getUser() {
    try {
      return JSON.parse(localStorage.getItem('user'));
    } catch {
      return null;
    }
  }

  getNavigationHTML() {
    const isAuthenticated = !!this.user;

    return `
      <header>
        <div class="container">
          <div class="header-content" style="display: flex; align-items: center; gap: 1rem;">
            <h1 style="margin: 0;">
              <a href="/" style="text-decoration: none; color: inherit;">mermantic</a>
            </h1>
            <a href="/" class="logo-link">
              <img src="/img/mermantic.svg" alt="mermantic logo" class="logo">
            </a>
          </div>
          <nav>
            <ul>
              <li><a href="/" ${this.currentPage === 'home' ? 'class="active"' : ''}>Home</a></li>
              <li><a href="/gallery.html" ${this.currentPage === 'gallery' ? 'class="active"' : ''}>Gallery</a></li>
              ${!isAuthenticated ? `
                <li><a href="/login.html" id="login-link" ${this.currentPage === 'login' ? 'class="active"' : ''}>Login</a></li>
                <li><a href="/register.html" id="register-link" ${this.currentPage === 'register' ? 'class="active"' : ''}>Register</a></li>
              ` : ''}
              ${isAuthenticated ? `
                <li><a href="/dashboard.html" id="dashboard-link" ${this.currentPage === 'dashboard' ? 'class="active"' : ''}>Dashboard</a></li>
                <li><a href="#" id="logout-link">Logout</a></li>
              ` : ''}
            </ul>
          </nav>
        </div>
      </header>
    `;
  }

  render(containerId = 'navigation-container') {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('Navigation container not found:', containerId);
      return;
    }

    container.innerHTML = this.getNavigationHTML();
    this.attachEventListeners();
  }

  attachEventListeners() {
    const logoutLink = document.getElementById('logout-link');
    if (logoutLink) {
      logoutLink.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleLogout();
      });
    }
  }

  async handleLogout() {
    try {
      // Call logout API
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });

      // Clear local storage
      localStorage.removeItem('user');
      
      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Logout error:', error);
      // Fallback: clear local storage and redirect anyway
      localStorage.removeItem('user');
      window.location.href = '/';
    }
  }

  // Method to update navigation when auth state changes
  updateAuthState() {
    this.user = this.getUser();
    this.render();
  }

  // Static method to get navigation for pages that need auth-aware navigation
  static getAuthAwareNavigation() {
    const nav = new NavigationComponent();
    return nav.getNavigationHTML();
  }
}

// Export for use in other scripts
window.NavigationComponent = NavigationComponent;
